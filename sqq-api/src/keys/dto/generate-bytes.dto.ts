import { <PERSON><PERSON>ption<PERSON>, IsInt, Min, Max } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class GenerateBytesDto {
  @ApiProperty({
    example: 32,
    description: 'Número de bytes a generar',
    minimum: 1,
    maximum: 1024,
    required: false,
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(1024)
  num_bytes?: number = 32;

  @ApiProperty({
    example: 1,
    description: 'Número de paquetes a generar',
    minimum: 1,
    maximum: 100,
    required: false,
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(100)
  packages?: number = 1;
}
