import { Injectable, ForbiddenException, BadRequestException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UsersService } from '../users/users.service';
import { SeqrngClientService, CtmConfig } from '../common/services/seqrng-client.service';
import { Key, KeyType, KeyStatus } from './entities/key.entity';
import { GenerateBytesDto } from './dto/generate-bytes.dto';
import { GenerateKeyDto } from './dto/generate-key.dto';
import { UploadKeyDto } from './dto/upload-key.dto';
import { UploadBatchKeysDto } from './dto/upload-batch-keys.dto';
import { GetKeysDto } from './dto/get-keys.dto';
import { KeyResponseDto, KeyListResponseDto } from './dto/key-response.dto';

@Injectable()
export class KeysService {
  private readonly logger = new Logger(KeysService.name);

  constructor(
    @InjectRepository(Key)
    private readonly keyRepository: Repository<Key>,
    private readonly usersService: UsersService,
    private readonly seqrngClientService: SeqrngClientService,
  ) {}

  /**
   * Generate random bytes using user's CTM configuration
   */
  async generateRandomBytes(userId: string, generateBytesDto: GenerateBytesDto) {
    this.logger.log(`Generating random bytes for user ${userId}`);

    const ctmConfig = await this.getUserCtmConfig(userId);

    // Create key record
    const keyRecord = this.keyRepository.create({
      name: `random-bytes-${Date.now()}`,
      type: KeyType.RANDOM_BYTES,
      algorithm: null,
      numBytes: generateBytesDto.num_bytes,
      status: KeyStatus.GENERATED,
      owner: null,
      exportable: false,
      uploadedToCtm: false,
      ctmKeyName: null,
      userId,
    });

    try {
      const result = await this.seqrngClientService.generateRandomBytes(
        generateBytesDto,
        ctmConfig,
      );

      // Update key record with success
      keyRecord.entropyReport = JSON.stringify(result.data?.entropy_report || {});
      await this.keyRepository.save(keyRecord);

      this.logger.log(`Successfully generated ${generateBytesDto.num_bytes} random bytes for user ${userId}`);
      return result;
    } catch (error) {
      // Update key record with error
      keyRecord.status = KeyStatus.FAILED;
      keyRecord.errorMessage = error.message;
      await this.keyRepository.save(keyRecord);

      this.logger.error(`Failed to generate random bytes for user ${userId}`, error);
      throw new BadRequestException(`Failed to generate random bytes: ${error.message}`);
    }
  }

  /**
   * Generate hexadecimal key using user's CTM configuration
   */
  async generateHexKey(userId: string, generateKeyDto: GenerateKeyDto) {
    this.logger.log(`Generating hex key for user ${userId}`);

    const ctmConfig = await this.getUserCtmConfig(userId);

    // Create key record
    const keyRecord = this.keyRepository.create({
      name: `hex-key-${Date.now()}`,
      type: KeyType.HEX_KEY,
      algorithm: null, // Will be set based on the generated key
      numBytes: generateKeyDto.num_bytes,
      status: KeyStatus.GENERATED,
      owner: null, // No owner specified for generated keys
      exportable: false,
      uploadedToCtm: false,
      ctmKeyName: null,
      userId,
    });

    try {
      const result = await this.seqrngClientService.generateHexKey(
        generateKeyDto,
        ctmConfig,
      );

      // Update key record with success
      keyRecord.entropyReport = JSON.stringify(result.data?.entropy_report || {});
      await this.keyRepository.save(keyRecord);

      this.logger.log(`Successfully generated hex key for user ${userId}`);
      return result;
    } catch (error) {
      // Update key record with error
      keyRecord.status = KeyStatus.FAILED;
      keyRecord.errorMessage = error.message;
      await this.keyRepository.save(keyRecord);

      this.logger.error(`Failed to generate hex key for user ${userId}`, error);
      throw new BadRequestException(`Failed to generate hex key: ${error.message}`);
    }
  }

  /**
   * Generate alphanumeric key using user's CTM configuration
   */
  async generateAlphanumericKey(userId: string, generateKeyDto: GenerateKeyDto) {
    this.logger.log(`Generating alphanumeric key for user ${userId}`);

    const ctmConfig = await this.getUserCtmConfig(userId);

    // Create key record
    const keyRecord = this.keyRepository.create({
      name: `alphanumeric-key-${Date.now()}`,
      type: KeyType.ALPHANUMERIC_KEY,
      algorithm: null, // Will be set based on the generated key
      numBytes: generateKeyDto.num_bytes,
      status: KeyStatus.GENERATED,
      owner: null, // No owner specified for generated keys
      exportable: false,
      uploadedToCtm: false,
      ctmKeyName: null,
      userId,
    });

    try {
      const result = await this.seqrngClientService.generateAlphanumericKey(
        generateKeyDto,
        ctmConfig,
      );

      // Update key record with success
      keyRecord.entropyReport = JSON.stringify(result.data?.entropy_report || {});
      await this.keyRepository.save(keyRecord);

      this.logger.log(`Successfully generated alphanumeric key for user ${userId}`);
      return result;
    } catch (error) {
      // Update key record with error
      keyRecord.status = KeyStatus.FAILED;
      keyRecord.errorMessage = error.message;
      await this.keyRepository.save(keyRecord);

      this.logger.error(`Failed to generate alphanumeric key for user ${userId}`, error);
      throw new BadRequestException(`Failed to generate alphanumeric key: ${error.message}`);
    }
  }

  /**
   * Upload key to CTM using user's configuration
   */
  async uploadKeyToCtm(userId: string, uploadKeyDto: UploadKeyDto) {
    this.logger.log(`Uploading key '${uploadKeyDto.key_name}' to CTM for user ${userId}`);

    const ctmConfig = await this.getUserCtmConfig(userId);

    // Create key record
    const keyRecord = this.keyRepository.create({
      name: uploadKeyDto.key_name,
      type: KeyType.HEX_KEY, // Assuming uploaded keys are hex by default
      algorithm: uploadKeyDto.algorithm || null,
      numBytes: uploadKeyDto.num_bytes || 32,
      status: KeyStatus.GENERATED,
      owner: uploadKeyDto.owner || null,
      exportable: uploadKeyDto.exportable || false,
      uploadedToCtm: false,
      ctmKeyName: uploadKeyDto.key_name,
      userId,
    });

    try {
      const result = await this.seqrngClientService.uploadKeyToCtm(
        uploadKeyDto,
        ctmConfig,
      );

      // Update key record with success
      keyRecord.status = KeyStatus.UPLOADED_TO_CTM;
      keyRecord.uploadedToCtm = true;
      keyRecord.entropyReport = JSON.stringify(result.data?.entropy_report || {});
      await this.keyRepository.save(keyRecord);

      this.logger.log(`Successfully uploaded key '${uploadKeyDto.key_name}' to CTM for user ${userId}`);
      return result;
    } catch (error) {
      // Update key record with error
      keyRecord.status = KeyStatus.FAILED;
      keyRecord.errorMessage = error.message;
      await this.keyRepository.save(keyRecord);

      this.logger.error(`Failed to upload key to CTM for user ${userId}`, error);
      throw new BadRequestException(`Failed to upload key to CTM: ${error.message}`);
    }
  }

  /**
   * Upload multiple keys to CTM using user's configuration
   */
  async uploadBatchKeysToCtm(userId: string, uploadBatchKeysDto: UploadBatchKeysDto) {
    this.logger.log(`Uploading batch keys with prefix '${uploadBatchKeysDto.key_name_prefix}' to CTM for user ${userId}`);
    
    const ctmConfig = await this.getUserCtmConfig(userId);
    
    try {
      const result = await this.seqrngClientService.uploadBatchKeysToCtm(
        uploadBatchKeysDto,
        ctmConfig,
      );

      this.logger.log(`Successfully uploaded batch keys to CTM for user ${userId}`);
      return result;
    } catch (error) {
      this.logger.error(`Failed to upload batch keys to CTM for user ${userId}`, error);
      throw new BadRequestException(`Failed to upload batch keys to CTM: ${error.message}`);
    }
  }

  /**
   * Check if key exists in CTM using user's configuration
   */
  async checkKeyExists(userId: string, keyName: string) {
    this.logger.log(`Checking if key '${keyName}' exists in CTM for user ${userId}`);
    
    const ctmConfig = await this.getUserCtmConfig(userId);
    
    try {
      const result = await this.seqrngClientService.checkKeyExists(keyName, ctmConfig);

      this.logger.log(`Successfully checked key existence for user ${userId}`);
      return result;
    } catch (error) {
      this.logger.error(`Failed to check key existence for user ${userId}`, error);
      throw new BadRequestException(`Failed to check key existence: ${error.message}`);
    }
  }

  /**
   * Get CTM authentication token using user's configuration
   */
  async getCtmToken(userId: string) {
    this.logger.log(`Getting CTM token for user ${userId}`);
    
    const ctmConfig = await this.getUserCtmConfig(userId);
    
    try {
      const result = await this.seqrngClientService.getCtmToken(ctmConfig);

      this.logger.log(`Successfully obtained CTM token for user ${userId}`);
      return result;
    } catch (error) {
      this.logger.error(`Failed to get CTM token for user ${userId}`, error);
      throw new BadRequestException(`Failed to get CTM token: ${error.message}`);
    }
  }

  /**
   * Get user's CTM configuration and validate it exists
   */
  private async getUserCtmConfig(userId: string): Promise<CtmConfig> {
    const ctmConfig = await this.usersService.getUserCtmConfig(userId);
    
    if (!ctmConfig) {
      throw new ForbiddenException(
        'User does not have CTM configuration. Please contact your administrator to set up CTM access.',
      );
    }
    
    return ctmConfig;
  }

  /**
   * Get user's keys with pagination and filters
   */
  async getUserKeys(userId: string, getKeysDto: GetKeysDto): Promise<KeyListResponseDto> {
    this.logger.log(`Getting keys for user ${userId}`);

    const {
      page = 1,
      limit = 10,
      type,
      algorithm,
      status,
      uploadedToCtm,
      search,
      sortBy = 'createdAt',
      sortOrder = 'DESC',
    } = getKeysDto;

    const queryBuilder = this.keyRepository
      .createQueryBuilder('key')
      .where('key.userId = :userId', { userId });

    // Apply filters
    if (type) {
      queryBuilder.andWhere('key.type = :type', { type });
    }

    if (algorithm) {
      queryBuilder.andWhere('key.algorithm = :algorithm', { algorithm });
    }

    if (status) {
      queryBuilder.andWhere('key.status = :status', { status });
    }

    if (uploadedToCtm !== undefined) {
      queryBuilder.andWhere('key.uploadedToCtm = :uploadedToCtm', { uploadedToCtm });
    }

    if (search) {
      queryBuilder.andWhere(
        '(key.name ILIKE :search OR key.ctmKeyName ILIKE :search)',
        { search: `%${search}%` }
      );
    }

    // Apply sorting
    const validSortFields = ['createdAt', 'updatedAt', 'name', 'type', 'status'];
    const sortField = validSortFields.includes(sortBy) ? sortBy : 'createdAt';
    queryBuilder.orderBy(`key.${sortField}`, sortOrder);

    // Apply pagination
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    // Execute query
    const [keys, total] = await queryBuilder.getManyAndCount();

    // Calculate statistics
    const allKeysQuery = this.keyRepository
      .createQueryBuilder('key')
      .where('key.userId = :userId', { userId });

    const [allKeys] = await allKeysQuery.getManyAndCount();
    const successful = await allKeysQuery
      .andWhere('key.status != :failedStatus', { failedStatus: KeyStatus.FAILED })
      .getCount();
    const failed = total - successful;
    const uploadedToCtmCount = await allKeysQuery
      .andWhere('key.uploadedToCtm = :uploaded', { uploaded: true })
      .getCount();

    // Transform to DTOs
    const keyDtos: KeyResponseDto[] = keys.map(key => ({
      id: key.id,
      name: key.name,
      type: key.type,
      algorithm: key.algorithm,
      numBytes: key.numBytes,
      status: key.status,
      owner: key.owner,
      exportable: key.exportable,
      uploadedToCtm: key.uploadedToCtm,
      ctmKeyName: key.ctmKeyName,
      entropyReport: key.entropyReport,
      errorMessage: key.errorMessage,
      createdAt: key.createdAt,
      updatedAt: key.updatedAt,
      isSuccessful: key.isSuccessful,
      displayName: key.displayName,
    }));

    return {
      keys: keyDtos,
      total,
      successful,
      failed,
      uploadedToCtm: uploadedToCtmCount,
    };
  }

  /**
   * Get a specific key by ID for a user
   */
  async getUserKey(userId: string, keyId: string): Promise<KeyResponseDto> {
    this.logger.log(`Getting key ${keyId} for user ${userId}`);

    const key = await this.keyRepository.findOne({
      where: { id: keyId, userId },
    });

    if (!key) {
      throw new BadRequestException('Key not found');
    }

    return {
      id: key.id,
      name: key.name,
      type: key.type,
      algorithm: key.algorithm,
      numBytes: key.numBytes,
      status: key.status,
      owner: key.owner,
      exportable: key.exportable,
      uploadedToCtm: key.uploadedToCtm,
      ctmKeyName: key.ctmKeyName,
      entropyReport: key.entropyReport,
      errorMessage: key.errorMessage,
      createdAt: key.createdAt,
      updatedAt: key.updatedAt,
      isSuccessful: key.isSuccessful,
      displayName: key.displayName,
    };
  }

  /**
   * Delete a key by ID for a user
   */
  async deleteUserKey(userId: string, keyId: string): Promise<void> {
    this.logger.log(`Deleting key ${keyId} for user ${userId}`);

    const result = await this.keyRepository.delete({
      id: keyId,
      userId,
    });

    if (result.affected === 0) {
      throw new BadRequestException('Key not found');
    }

    this.logger.log(`Successfully deleted key ${keyId} for user ${userId}`);
  }
}
