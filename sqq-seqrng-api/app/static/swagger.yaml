openapi: 3.0.3
info:
  title: SeQRNG-CTM REST API
  description: |
    A Flask REST API for integrating SeQRNG quantum random number generator with Thales CipherTrust Manager.

    ## Features
    - **Quantum Random Number Generation**: Generate cryptographically secure random keys using SeQRNG quantum technology
    - **CipherTrust Manager Integration**: Upload and manage keys in Thales CipherTrust Manager
    - **Multiple Key Formats**: Support for bytes, hexadecimal, and alphanumeric key generation
    - **Batch Operations**: Upload multiple keys in a single operation
    - **Health Monitoring**: Real-time API health and configuration status
    - **Interactive Documentation**: Swagger UI for easy API exploration

    ## Security
    - All key material is generated using quantum random number generators
    - Keys are transmitted securely using Base64 encoding
    - Integration with enterprise-grade Thales CipherTrust Manager
    - Configurable key ownership and exportability settings

    ## Supported Algorithms
    - **AES**: 16, 24, or 32 bytes (128, 192, 256 bits)
    - **RSA**: Variable length key material
    - **HMAC**: Variable length key material
  version: 1.0.0
  contact:
    name: SeQRNG-CTM API Support
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT
    
servers:
  - url: http://localhost:5000
    description: Local development server
  - url: https://api.example.com
    description: Production server

paths:
  /api/v1/health:
    get:
      summary: Health check endpoint
      description: Check API health status and configuration
      operationId: healthCheck
      tags:
        - Health
      responses:
        '200':
          description: Health check successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthResponse'
        '500':
          description: Health check failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /api/v1/docs:
    get:
      summary: API documentation endpoint
      description: Get comprehensive API documentation
      operationId: getApiDocs
      tags:
        - Documentation
      responses:
        '200':
          description: API documentation retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'

  /api/v1/config/status:
    get:
      summary: Get configuration status
      description: Check if SeQRNG and CTM configurations are loaded
      operationId: getConfigStatus
      tags:
        - Configuration
      responses:
        '200':
          description: Configuration status retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConfigStatusResponse'
        '500':
          description: Failed to get configuration status
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /api/v1/keys/generate/bytes:
    post:
      summary: Generate random bytes from SeQRNG
      description: Generate quantum random bytes using SeQRNG
      operationId: generateRandomBytes
      tags:
        - Key Generation
      requestBody:
        description: Parameters for random byte generation
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GenerateBytesRequest'
      responses:
        '200':
          description: Random bytes generated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenerateBytesResponse'
        '400':
          description: Invalid parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Generation failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /api/v1/keys/generate/hex:
    post:
      summary: Generate hexadecimal key from SeQRNG
      description: Generate quantum random hexadecimal key
      operationId: generateHexKey
      tags:
        - Key Generation
      requestBody:
        description: Parameters for hex key generation
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GenerateKeyRequest'
      responses:
        '200':
          description: Hex key generated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenerateHexResponse'
        '400':
          description: Invalid parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Generation failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /api/v1/keys/generate/alphanumeric:
    post:
      summary: Generate alphanumeric key from SeQRNG
      description: Generate quantum random alphanumeric key
      operationId: generateAlphanumericKey
      tags:
        - Key Generation
      requestBody:
        description: Parameters for alphanumeric key generation
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GenerateKeyRequest'
      responses:
        '200':
          description: Alphanumeric key generated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenerateAlphanumericResponse'
        '400':
          description: Invalid parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Generation failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /api/v1/ctm/auth/token:
    get:
      summary: Get CTM authentication token
      description: Obtain authentication token for Thales CipherTrust Manager
      operationId: getCTMToken
      tags:
        - CTM Management
      responses:
        '200':
          description: CTM token obtained successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CTMTokenResponse'
        '500':
          description: Authentication failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /api/v1/ctm/keys/{key_name}/exists:
    get:
      summary: Check if key exists in CTM
      description: Verify if a specific key exists in Thales CipherTrust Manager
      operationId: checkKeyExists
      tags:
        - CTM Management
      parameters:
        - name: key_name
          in: path
          required: true
          description: Name of the key to check
          schema:
            type: string
            pattern: '^[a-zA-Z0-9_-]+$'
            maxLength: 100
      responses:
        '200':
          description: Key existence check completed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/KeyExistsResponse'
        '500':
          description: Check failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /api/v1/ctm/keys/upload:
    post:
      summary: Upload single key to CTM
      description: Upload a single key to Thales CipherTrust Manager
      operationId: uploadKeyToCTM
      tags:
        - CTM Management
      requestBody:
        required: true
        description: Key upload parameters
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UploadKeyRequest'
      responses:
        '200':
          description: Key uploaded successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UploadKeyResponse'
        '400':
          description: Invalid parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '409':
          description: Key already exists
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Upload failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /api/v1/ctm/keys/upload/batch:
    post:
      summary: Upload multiple keys to CTM
      description: Upload multiple keys to Thales CipherTrust Manager in batch
      operationId: uploadKeysBatchToCTM
      tags:
        - CTM Management
      requestBody:
        required: true
        description: Batch upload parameters
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BatchUploadRequest'
      responses:
        '200':
          description: Batch upload completed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BatchUploadResponse'
        '400':
          description: Invalid parameters or no keys uploaded
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Batch upload failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /docs:
    get:
      summary: Swagger UI interface
      description: Interactive API documentation using Swagger UI
      operationId: getSwaggerUI
      tags:
        - Documentation
      responses:
        '200':
          description: Swagger UI page served successfully
          content:
            text/html:
              schema:
                type: string
        '404':
          description: Swagger UI not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /swagger.yaml:
    get:
      summary: OpenAPI specification file
      description: Get the OpenAPI 3.0.3 specification in YAML format
      operationId: getOpenAPISpec
      tags:
        - Documentation
      responses:
        '200':
          description: OpenAPI specification retrieved successfully
          content:
            application/x-yaml:
              schema:
                type: string
                description: OpenAPI 3.0.3 specification in YAML format
        '404':
          description: OpenAPI specification not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

components:
  schemas:
    SuccessResponse:
      type: object
      properties:
        status:
          type: string
          enum: [success]
        message:
          type: string
          description: Human-readable success message
        data:
          type: object
          description: Response data payload
        timestamp:
          type: string
          format: date-time
          description: ISO 8601 timestamp when the response was generated
      required:
        - status
        - message
        - data
        - timestamp
      example:
        status: "success"
        message: "Operation completed successfully"
        data: {}
        timestamp: "2024-01-15T10:30:00Z"

    ErrorResponse:
      type: object
      properties:
        status:
          type: string
          enum: [error]
        error_type:
          type: string
          enum:
            - validation_error
            - configuration_error
            - authentication_error
            - upload_error
            - check_error
            - generation_error
            - internal_error
            - not_found
          description: |
            Error type classification:
            - `validation_error`: Invalid request parameters
            - `configuration_error`: Missing or invalid configuration
            - `authentication_error`: CTM authentication failed
            - `upload_error`: Failed to upload key to CTM
            - `check_error`: Failed to check key existence
            - `generation_error`: Failed to generate random data
            - `internal_error`: Internal server error
            - `not_found`: Resource not found
        message:
          type: string
          description: Human-readable error message
        timestamp:
          type: string
          format: date-time
          description: ISO 8601 timestamp when the error occurred
      required:
        - status
        - error_type
        - message
        - timestamp
      example:
        status: "error"
        error_type: "validation_error"
        message: "Invalid key name: must contain only alphanumeric characters, underscores, and hyphens"
        timestamp: "2024-01-15T10:30:00Z"

    HealthResponse:
      allOf:
        - $ref: '#/components/schemas/SuccessResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                service:
                  type: string
                  example: "SeQRNG-CTM API"
                version:
                  type: string
                  example: "1.0.0"
                status:
                  type: string
                  example: "healthy"
                configuration_loaded:
                  type: boolean
                endpoints:
                  type: object
                  properties:
                    key_generation:
                      type: string
                      example: "/api/v1/keys/generate/*"
                    ctm_management:
                      type: string
                      example: "/api/v1/ctm/*"
                    health:
                      type: string
                      example: "/api/v1/health"
                    config:
                      type: string
                      example: "/api/v1/config/status"
                    docs:
                      type: string
                      example: "/api/v1/docs"

    ConfigStatusResponse:
      allOf:
        - $ref: '#/components/schemas/SuccessResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                configuration_loaded:
                  type: boolean
                seqrng_url:
                  type: string
                  example: "https://seqrng.example.com"
                ctm_url:
                  type: string
                  example: "https://ctm.example.com"
                ctm_domain:
                  type: string
                  example: "root"

    EntropyReport:
      type: object
      properties:
        error_string:
          type: string
        entropy_string:
          type: string
        entropy_status:
          type: string
      required:
        - error_string
        - entropy_string
        - entropy_status

    GenerateBytesRequest:
      type: object
      properties:
        num_bytes:
          type: integer
          minimum: 1
          maximum: 1024
          default: 32
          description: Number of bytes to generate
          example: 32
        packages:
          type: integer
          minimum: 1
          maximum: 100
          default: 1
          description: Number of packages to request from SeQRNG
          example: 1

    GenerateKeyRequest:
      type: object
      properties:
        num_bytes:
          type: integer
          minimum: 1
          maximum: 1024
          default: 32
          description: Number of bytes to generate
          example: 32

    GenerateBytesResponse:
      allOf:
        - $ref: '#/components/schemas/SuccessResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                random_bytes_base64:
                  type: string
                  format: byte
                  description: Base64 encoded random bytes
                num_bytes:
                  type: integer
                packages:
                  type: integer
                entropy_report:
                  $ref: '#/components/schemas/EntropyReport'

    GenerateHexResponse:
      allOf:
        - $ref: '#/components/schemas/SuccessResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                hex_key:
                  type: string
                  pattern: '^[0-9a-fA-F]+$'
                  description: Hexadecimal key
                num_bytes:
                  type: integer
                entropy_report:
                  $ref: '#/components/schemas/EntropyReport'

    GenerateAlphanumericResponse:
      allOf:
        - $ref: '#/components/schemas/SuccessResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                alphanumeric_key:
                  type: string
                  pattern: '^[a-zA-Z0-9]+$'
                  description: Alphanumeric key
                num_bytes:
                  type: integer
                entropy_report:
                  $ref: '#/components/schemas/EntropyReport'

    CTMTokenResponse:
      allOf:
        - $ref: '#/components/schemas/SuccessResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                token_obtained:
                  type: boolean
                ctm_url:
                  type: string

    KeyExistsResponse:
      allOf:
        - $ref: '#/components/schemas/SuccessResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                key_name:
                  type: string
                exists:
                  type: boolean

    UploadKeyRequest:
      type: object
      required:
        - key_name
        - algorithm
        - num_bytes
        - owner
      properties:
        key_name:
          type: string
          pattern: '^[a-zA-Z0-9_-]+$'
          maxLength: 100
          description: Name of the key (alphanumeric, underscores, hyphens only)
          example: "my_quantum_key_001"
        algorithm:
          type: string
          enum: [AES, RSA, HMAC]
          description: Key algorithm
          example: "AES"
        num_bytes:
          type: integer
          minimum: 1
          maximum: 1024
          description: Number of bytes for the key (AES supports 16, 24, 32 bytes)
          example: 32
        owner:
          type: string
          description: Key owner
          example: "api_user"
        exportable:
          type: boolean
          default: false
          description: Whether the key is exportable
        key_material_base64:
          type: string
          format: byte
          description: Optional base64 encoded key material (generates new from SeQRNG if not provided)

    UploadKeyResponse:
      allOf:
        - $ref: '#/components/schemas/SuccessResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                key_name:
                  type: string
                algorithm:
                  type: string
                num_bytes:
                  type: integer
                owner:
                  type: string
                exportable:
                  type: boolean
                upload_successful:
                  type: boolean
                entropy_report:
                  type: object
                  properties:
                    source:
                      type: string
                      enum: [provided, seqrng]
                    error_string:
                      type: string
                    entropy_string:
                      type: string
                    entropy_status:
                      type: string
                key_material_base64:
                  type: string
                  format: byte
                  description: Base64 encoded key material that was uploaded

    BatchUploadRequest:
      type: object
      required:
        - key_base_name
        - algorithm
        - num_bytes
        - key_count
        - owner
      properties:
        key_base_name:
          type: string
          pattern: '^[a-zA-Z0-9_-]+$'
          maxLength: 100
          description: Base name for keys (will be appended with numbers, e.g., mykey_001, mykey_002)
          example: "quantum_key"
        algorithm:
          type: string
          enum: [AES, RSA, HMAC]
          description: Key algorithm
          example: "AES"
        num_bytes:
          type: integer
          minimum: 1
          maximum: 1024
          description: Number of bytes for each key (AES supports 16, 24, 32)
          example: 32
        owner:
          type: string
          description: Key owner
          example: "api_user"
        exportable:
          type: boolean
          default: false
          description: Whether the keys are exportable
        key_count:
          type: integer
          minimum: 1
          maximum: 100
          description: Number of keys to generate
          example: 5

    BatchUploadResponse:
      allOf:
        - $ref: '#/components/schemas/SuccessResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                key_base_name:
                  type: string
                algorithm:
                  type: string
                num_bytes:
                  type: integer
                key_count:
                  type: integer
                owner:
                  type: string
                exportable:
                  type: boolean
                uploaded_count:
                  type: integer
                failed_count:
                  type: integer
                total_requested:
                  type: integer
                uploaded_keys:
                  type: array
                  items:
                    type: object
                    properties:
                      key_name:
                        type: string
                      algorithm:
                        type: string
                      num_bytes:
                        type: integer
                      owner:
                        type: string
                      unexportable:
                        type: boolean
                      upload_successful:
                        type: boolean
                      key_material_base64:
                        type: string
                        format: byte
                failed_keys:
                  type: array
                  items:
                    type: object
                    properties:
                      key_name:
                        type: string
                      reason:
                        type: string
                entropy_reports:
                  type: array
                  items:
                    type: object
                    properties:
                      source:
                        type: string
                        enum: [seqrng]
                      error_string:
                        type: string
                      entropy_string:
                        type: string
                      entropy_status:
                        type: string

tags:
  - name: Health
    description: Health monitoring endpoints
  - name: Documentation
    description: API documentation endpoints
  - name: Configuration
    description: Configuration management endpoints
  - name: Key Generation
    description: Quantum random key generation endpoints
  - name: CTM Management
    description: Thales CipherTrust Manager integration endpoints
