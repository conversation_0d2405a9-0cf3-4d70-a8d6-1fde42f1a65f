import { Injectable, ForbiddenException, BadRequestException, Logger } from '@nestjs/common';
import { UsersService } from '../users/users.service';
import { SeqrngClientService, CtmConfig } from '../common/services/seqrng-client.service';
import { GenerateBytesDto } from './dto/generate-bytes.dto';
import { GenerateKeyDto } from './dto/generate-key.dto';
import { UploadKeyDto } from './dto/upload-key.dto';
import { UploadBatchKeysDto } from './dto/upload-batch-keys.dto';

@Injectable()
export class KeysService {
  private readonly logger = new Logger(KeysService.name);

  constructor(
    private readonly usersService: UsersService,
    private readonly seqrngClientService: SeqrngClientService,
  ) {}

  /**
   * Generate random bytes using user's CTM configuration
   */
  async generateRandomBytes(userId: string, generateBytesDto: GenerateBytesDto) {
    this.logger.log(`Generating random bytes for user ${userId}`);
    
    const ctmConfig = await this.getUserCtmConfig(userId);
    
    try {
      const result = await this.seqrngClientService.generateRandomBytes(
        generateBytesDto,
        ctmConfig,
      );

      this.logger.log(`Successfully generated ${generateBytesDto.num_bytes} random bytes for user ${userId}`);
      return result;
    } catch (error) {
      this.logger.error(`Failed to generate random bytes for user ${userId}`, error);
      throw new BadRequestException(`Failed to generate random bytes: ${error.message}`);
    }
  }

  /**
   * Generate hexadecimal key using user's CTM configuration
   */
  async generateHexKey(userId: string, generateKeyDto: GenerateKeyDto) {
    this.logger.log(`Generating hex key for user ${userId}`);
    
    const ctmConfig = await this.getUserCtmConfig(userId);
    
    try {
      const result = await this.seqrngClientService.generateHexKey(
        generateKeyDto,
        ctmConfig,
      );

      this.logger.log(`Successfully generated hex key for user ${userId}`);
      return result;
    } catch (error) {
      this.logger.error(`Failed to generate hex key for user ${userId}`, error);
      throw new BadRequestException(`Failed to generate hex key: ${error.message}`);
    }
  }

  /**
   * Generate alphanumeric key using user's CTM configuration
   */
  async generateAlphanumericKey(userId: string, generateKeyDto: GenerateKeyDto) {
    this.logger.log(`Generating alphanumeric key for user ${userId}`);
    
    const ctmConfig = await this.getUserCtmConfig(userId);
    
    try {
      const result = await this.seqrngClientService.generateAlphanumericKey(
        generateKeyDto,
        ctmConfig,
      );

      this.logger.log(`Successfully generated alphanumeric key for user ${userId}`);
      return result;
    } catch (error) {
      this.logger.error(`Failed to generate alphanumeric key for user ${userId}`, error);
      throw new BadRequestException(`Failed to generate alphanumeric key: ${error.message}`);
    }
  }

  /**
   * Upload key to CTM using user's configuration
   */
  async uploadKeyToCtm(userId: string, uploadKeyDto: UploadKeyDto) {
    this.logger.log(`Uploading key '${uploadKeyDto.key_name}' to CTM for user ${userId}`);
    
    const ctmConfig = await this.getUserCtmConfig(userId);
    
    try {
      const result = await this.seqrngClientService.uploadKeyToCtm(
        uploadKeyDto,
        ctmConfig,
      );

      this.logger.log(`Successfully uploaded key '${uploadKeyDto.key_name}' to CTM for user ${userId}`);
      return result;
    } catch (error) {
      this.logger.error(`Failed to upload key to CTM for user ${userId}`, error);
      throw new BadRequestException(`Failed to upload key to CTM: ${error.message}`);
    }
  }

  /**
   * Upload multiple keys to CTM using user's configuration
   */
  async uploadBatchKeysToCtm(userId: string, uploadBatchKeysDto: UploadBatchKeysDto) {
    this.logger.log(`Uploading batch keys with prefix '${uploadBatchKeysDto.key_name_prefix}' to CTM for user ${userId}`);
    
    const ctmConfig = await this.getUserCtmConfig(userId);
    
    try {
      const result = await this.seqrngClientService.uploadBatchKeysToCtm(
        uploadBatchKeysDto,
        ctmConfig,
      );

      this.logger.log(`Successfully uploaded batch keys to CTM for user ${userId}`);
      return result;
    } catch (error) {
      this.logger.error(`Failed to upload batch keys to CTM for user ${userId}`, error);
      throw new BadRequestException(`Failed to upload batch keys to CTM: ${error.message}`);
    }
  }

  /**
   * Check if key exists in CTM using user's configuration
   */
  async checkKeyExists(userId: string, keyName: string) {
    this.logger.log(`Checking if key '${keyName}' exists in CTM for user ${userId}`);
    
    const ctmConfig = await this.getUserCtmConfig(userId);
    
    try {
      const result = await this.seqrngClientService.checkKeyExists(keyName, ctmConfig);

      this.logger.log(`Successfully checked key existence for user ${userId}`);
      return result;
    } catch (error) {
      this.logger.error(`Failed to check key existence for user ${userId}`, error);
      throw new BadRequestException(`Failed to check key existence: ${error.message}`);
    }
  }

  /**
   * Get CTM authentication token using user's configuration
   */
  async getCtmToken(userId: string) {
    this.logger.log(`Getting CTM token for user ${userId}`);
    
    const ctmConfig = await this.getUserCtmConfig(userId);
    
    try {
      const result = await this.seqrngClientService.getCtmToken(ctmConfig);

      this.logger.log(`Successfully obtained CTM token for user ${userId}`);
      return result;
    } catch (error) {
      this.logger.error(`Failed to get CTM token for user ${userId}`, error);
      throw new BadRequestException(`Failed to get CTM token: ${error.message}`);
    }
  }

  /**
   * Get user's CTM configuration and validate it exists
   */
  private async getUserCtmConfig(userId: string): Promise<CtmConfig> {
    const ctmConfig = await this.usersService.getUserCtmConfig(userId);
    
    if (!ctmConfig) {
      throw new ForbiddenException(
        'User does not have CTM configuration. Please contact your administrator to set up CTM access.',
      );
    }
    
    return ctmConfig;
  }
}
