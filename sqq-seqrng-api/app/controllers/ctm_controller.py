"""
CTM controller for CipherTrust Manager endpoints
"""

import base64
from flask import Blueprint, request, current_app
from app.utils.response_helpers import create_success_response, create_error_response
from app.utils.validators import validate_json_request
from app.services.ctm_service import CTMService
from app.services.seqrng_service import SeQRNGService
from app.services.validation_service import ValidationService
from sq_ctm_modules_v1 import group_bytes

ctm_bp = Blueprint('ctm', __name__)


def get_ctm_service():
    """Get CTM service instance"""
    ctm_config = current_app.config.get('CTM_CONFIG')
    if not ctm_config:
        raise Exception("CTM configuration not loaded")
    return CTMService(ctm_config)


def get_seqrng_service():
    """Get SeQRNG service instance"""
    seqrng_config = current_app.config.get('SEQRNG_CONFIG')
    if not seqrng_config:
        raise Exception("SeQRNG configuration not loaded")
    return SeQRNGService(seqrng_config)


@ctm_bp.route('/api/v1/ctm/auth/token', methods=['GET'])
def get_ctm_token():
    """Get CTM API token"""
    try:
        ctm_service = get_ctm_service()
        api_key = ctm_service.get_api_key()

        response_data = {
            "token_obtained": True,
            "ctm_url": ctm_service.base_url
        }

        return create_success_response(response_data, "CTM token obtained successfully")

    except Exception as e:
        return create_error_response(f"Failed to get CTM token: {str(e)}", 500, "authentication_error")


@ctm_bp.route('/api/v1/ctm/keys/<key_name>/exists', methods=['GET'])
def check_key_exists(key_name):
    """Check if a key exists in CTM"""
    try:
        ctm_service = get_ctm_service()
        exists = ctm_service.check_key_exists(key_name)

        response_data = {
            "key_name": key_name,
            "exists": exists
        }

        return create_success_response(response_data, f"Key existence check completed")

    except Exception as e:
        return create_error_response(f"Failed to check key existence: {str(e)}", 500, "check_error")


@ctm_bp.route('/api/v1/ctm/keys/upload', methods=['POST'])
def upload_key_to_ctm():
    """Upload a single key to CTM"""
    try:
        # Validate JSON request
        json_error = validate_json_request()
        if json_error:
            return json_error

        # Get request data
        data = request.get_json()
        key_name = data.get('key_name')
        algorithm = data.get('algorithm')
        num_bytes = data.get('num_bytes')
        owner = data.get('owner')
        exportable = data.get('exportable', False)
        key_material_b64 = data.get('key_material_base64')

        # Validate request
        validation_errors = ValidationService.validate_key_upload_request(
            key_name, algorithm, num_bytes, owner, exportable
        )
        if validation_errors:
            return create_error_response("; ".join(validation_errors), 400, "validation_error")

        # Get services
        ctm_service = get_ctm_service()
        seqrng_service = get_seqrng_service()

        # Check if key already exists
        if ctm_service.check_key_exists(key_name):
            return create_error_response(f"Key '{key_name}' already exists in CTM", 409, "conflict_error")

        # Prepare additional metadata
        additional_meta = {
            "cte": {
                "is_used": True,
                "cte_versioned": True,
                "encryption_mode": "CBC",
                "unique_to_client": False,
                "persistent_on_client": True,
                "unique_to_client_format": ""
            },
            "permissions": {
                "ReadKey": [
                    "CTE Clients"
                ],
                "ExportKey": [
                    "CTE Clients"
                ]
            }
        }

        # Get key material
        if key_material_b64:
            # Use provided key material
            entropy_report = {"source": "provided", "error_string": "N/A", "entropy_string": "N/A", "entropy_status": "N/A"}
            result = ctm_service.upload_key_with_provided_material(
                key_name, key_material_b64, algorithm, owner, not exportable, additional_meta
            )
        else:
            # Generate new key material from SeQRNG
            key_material, entropy_report = seqrng_service.generate_key_material(num_bytes)
            result = ctm_service.upload_key(
                key_name, key_material, algorithm, owner, not exportable, additional_meta
            )

        # Prepare response
        response_data = {
            "key_name": result["key_name"],
            "algorithm": result["algorithm"],
            "num_bytes": result["num_bytes"],
            "owner": result["owner"],
            "exportable": exportable,
            "upload_successful": result["upload_successful"],
            "entropy_report": entropy_report,
            "key_material_base64": result["key_material_base64"]
        }

        return create_success_response(response_data, f"Key '{key_name}' uploaded successfully to CTM")

    except Exception as e:
        return create_error_response(f"Failed to upload key to CTM: {str(e)}", 500, "upload_error")


@ctm_bp.route('/api/v1/ctm/keys/upload/batch', methods=['POST'])
def upload_batch_keys_to_ctm():
    """Upload multiple keys to CTM"""
    try:
        # Validate JSON request
        json_error = validate_json_request()
        if json_error:
            return json_error

        # Get request data
        data = request.get_json()
        key_base_name = data.get('key_base_name')
        algorithm = data.get('algorithm')
        num_bytes = data.get('num_bytes')
        key_count = data.get('key_count')
        owner = data.get('owner')
        exportable = data.get('exportable', False)

        # Validate request
        validation_errors = ValidationService.validate_batch_upload_request(
            key_base_name, algorithm, num_bytes, key_count, owner, exportable
        )
        if validation_errors:
            return create_error_response("; ".join(validation_errors), 400, "validation_error")

        # Get services
        ctm_service = get_ctm_service()
        seqrng_service = get_seqrng_service()

        # Generate key material for all keys
        print(f"🔥 Generating {key_count} keys of {num_bytes} bytes each...")
        key_material_list = []
        entropy_reports = []

        for i in range(key_count):
            key_material, entropy_report = seqrng_service.generate_key_material(num_bytes)
            key_material_list.append(key_material)
            entropy_reports.append(entropy_report)

        # Prepare additional metadata
        additional_meta = {
            "cte": {
                "is_used": True,
                "cte_versioned": True,
                "encryption_mode": "CBC",
                "unique_to_client": False,
                "persistent_on_client": True,
                "unique_to_client_format": ""
            },
            "permissions": {
                "ReadKey": [
                    "CTE Clients"
                ],
                "ExportKey": [
                    "CTE Clients"
                ]
            }
        }

        # Upload keys to CTM
        result = ctm_service.upload_multiple_keys(
            key_base_name, key_count, key_material_list, algorithm, owner, not exportable, additional_meta
        )

        # Prepare response
        response_data = {
            "key_base_name": key_base_name,
            "algorithm": result["algorithm"],
            "num_bytes": num_bytes,
            "key_count": key_count,
            "owner": result["owner"],
            "exportable": exportable,
            "uploaded_count": result["uploaded_count"],
            "failed_count": result["failed_count"],
            "total_requested": result["total_requested"],
            "uploaded_keys": result["uploaded_keys"],
            "failed_keys": result["failed_keys"],
            "entropy_reports": entropy_reports
        }

        message = f"Batch upload completed: {result['uploaded_count']}/{result['total_requested']} keys uploaded successfully"
        return create_success_response(response_data, message)

    except Exception as e:
        return create_error_response(f"Failed to upload batch keys to CTM: {str(e)}", 500, "batch_upload_error")
