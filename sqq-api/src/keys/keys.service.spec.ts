import { Test, TestingModule } from '@nestjs/testing';
import { ForbiddenException, BadRequestException } from '@nestjs/common';
import { KeysService } from './keys.service';
import { UsersService } from '../users/users.service';
import { SeqrngClientService } from '../common/services/seqrng-client.service';

describe('KeysService', () => {
  let service: KeysService;
  let usersService: UsersService;
  let seqrngClientService: SeqrngClientService;

  const mockUsersService = {
    getUserCtmConfig: jest.fn(),
  };

  const mockSeqrngClientService = {
    generateRandomBytes: jest.fn(),
    generateHexKey: jest.fn(),
    generateAlphanumericKey: jest.fn(),
    uploadKeyToCtm: jest.fn(),
    uploadBatchKeysToCtm: jest.fn(),
    checkKeyExists: jest.fn(),
    getCtmToken: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        KeysService,
        {
          provide: UsersService,
          useValue: mockUsersService,
        },
        {
          provide: SeqrngClientService,
          useValue: mockSeqrngClientService,
        },
      ],
    }).compile();

    service = module.get<KeysService>(KeysService);
    usersService = module.get<UsersService>(UsersService);
    seqrngClientService = module.get<SeqrngClientService>(SeqrngClientService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('generateRandomBytes', () => {
    it('should generate random bytes successfully', async () => {
      const userId = 'test-user-id';
      const generateBytesDto = { num_bytes: 32, packages: 1 };
      const mockCtmConfig = {
        ipAddress: 'https://ctm.example.com',
        username: 'ctm_user',
        password: 'ctm_pass',
        domain: 'root',
      };
      const mockResponse = {
        status: 'success',
        message: 'Random bytes generated successfully',
        data: { random_bytes_base64: 'base64data' },
        timestamp: '2023-01-01T00:00:00Z',
      };

      mockUsersService.getUserCtmConfig.mockResolvedValue(mockCtmConfig);
      mockSeqrngClientService.generateRandomBytes.mockResolvedValue(mockResponse);

      const result = await service.generateRandomBytes(userId, generateBytesDto);

      expect(mockUsersService.getUserCtmConfig).toHaveBeenCalledWith(userId);
      expect(mockSeqrngClientService.generateRandomBytes).toHaveBeenCalledWith(
        generateBytesDto,
        mockCtmConfig,
      );
      expect(result).toEqual(mockResponse);
    });

    it('should throw ForbiddenException when user has no CTM config', async () => {
      const userId = 'test-user-id';
      const generateBytesDto = { num_bytes: 32, packages: 1 };

      mockUsersService.getUserCtmConfig.mockResolvedValue(null);

      await expect(service.generateRandomBytes(userId, generateBytesDto)).rejects.toThrow(
        ForbiddenException,
      );
    });

    it('should throw BadRequestException when SeQRNG API fails', async () => {
      const userId = 'test-user-id';
      const generateBytesDto = { num_bytes: 32, packages: 1 };
      const mockCtmConfig = {
        ipAddress: 'https://ctm.example.com',
        username: 'ctm_user',
        password: 'ctm_pass',
        domain: 'root',
      };

      mockUsersService.getUserCtmConfig.mockResolvedValue(mockCtmConfig);
      mockSeqrngClientService.generateRandomBytes.mockRejectedValue(
        new Error('SeQRNG API error'),
      );

      await expect(service.generateRandomBytes(userId, generateBytesDto)).rejects.toThrow(
        BadRequestException,
      );
    });
  });

  describe('uploadKeyToCtm', () => {
    it('should upload key to CTM successfully', async () => {
      const userId = 'test-user-id';
      const uploadKeyDto = {
        key_name: 'test-key',
        algorithm: 'AES' as any,
        num_bytes: 32,
        owner: 'api_user',
        exportable: false,
      };
      const mockCtmConfig = {
        ipAddress: 'https://ctm.example.com',
        username: 'ctm_user',
        password: 'ctm_pass',
        domain: 'root',
      };
      const mockResponse = {
        status: 'success',
        message: 'Key uploaded to CTM successfully',
        data: { key_name: 'test-key', upload_successful: true },
        timestamp: '2023-01-01T00:00:00Z',
      };

      mockUsersService.getUserCtmConfig.mockResolvedValue(mockCtmConfig);
      mockSeqrngClientService.uploadKeyToCtm.mockResolvedValue(mockResponse);

      const result = await service.uploadKeyToCtm(userId, uploadKeyDto);

      expect(mockUsersService.getUserCtmConfig).toHaveBeenCalledWith(userId);
      expect(mockSeqrngClientService.uploadKeyToCtm).toHaveBeenCalledWith(
        uploadKeyDto,
        mockCtmConfig,
      );
      expect(result).toEqual(mockResponse);
    });
  });

  describe('checkKeyExists', () => {
    it('should check key existence successfully', async () => {
      const userId = 'test-user-id';
      const keyName = 'test-key';
      const mockCtmConfig = {
        ipAddress: 'https://ctm.example.com',
        username: 'ctm_user',
        password: 'ctm_pass',
        domain: 'root',
      };
      const mockResponse = {
        status: 'success',
        message: 'Key existence check completed',
        data: { key_name: 'test-key', exists: true },
        timestamp: '2023-01-01T00:00:00Z',
      };

      mockUsersService.getUserCtmConfig.mockResolvedValue(mockCtmConfig);
      mockSeqrngClientService.checkKeyExists.mockResolvedValue(mockResponse);

      const result = await service.checkKeyExists(userId, keyName);

      expect(mockUsersService.getUserCtmConfig).toHaveBeenCalledWith(userId);
      expect(mockSeqrngClientService.checkKeyExists).toHaveBeenCalledWith(
        keyName,
        mockCtmConfig,
      );
      expect(result).toEqual(mockResponse);
    });
  });
});
