import {
  Controller,
  Post,
  Get,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  BadRequestException,
  ForbiddenException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { KeysService } from './keys.service';
import { GenerateBytesDto } from './dto/generate-bytes.dto';
import { GenerateKeyDto } from './dto/generate-key.dto';
import { UploadKeyDto } from './dto/upload-key.dto';
import { UploadBatchKeysDto } from './dto/upload-batch-keys.dto';
import { GetKeysDto } from './dto/get-keys.dto';
import { KeyResponseDto, KeyListResponseDto } from './dto/key-response.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('Keys')
@Controller('keys')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class KeysController {
  constructor(private readonly keysService: KeysService) {}

  @Post('generate/bytes')
  @ApiOperation({ summary: 'Generar bytes aleatorios cuánticos' })
  @ApiResponse({
    status: 201,
    description: 'Bytes aleatorios generados exitosamente',
  })
  @ApiResponse({
    status: 400,
    description: 'Parámetros inválidos',
  })
  @ApiResponse({
    status: 403,
    description: 'Usuario no tiene configuración CTM',
  })
  async generateRandomBytes(
    @Body() generateBytesDto: GenerateBytesDto,
    @Request() req: any,
  ) {
    const userId = req.user.id;
    return this.keysService.generateRandomBytes(userId, generateBytesDto);
  }

  @Post('generate/hex')
  @ApiOperation({ summary: 'Generar llave hexadecimal cuántica' })
  @ApiResponse({
    status: 201,
    description: 'Llave hexadecimal generada exitosamente',
  })
  @ApiResponse({
    status: 400,
    description: 'Parámetros inválidos',
  })
  @ApiResponse({
    status: 403,
    description: 'Usuario no tiene configuración CTM',
  })
  async generateHexKey(
    @Body() generateKeyDto: GenerateKeyDto,
    @Request() req: any,
  ) {
    const userId = req.user.id;
    return this.keysService.generateHexKey(userId, generateKeyDto);
  }

  @Post('generate/alphanumeric')
  @ApiOperation({ summary: 'Generar llave alfanumérica cuántica' })
  @ApiResponse({
    status: 201,
    description: 'Llave alfanumérica generada exitosamente',
  })
  @ApiResponse({
    status: 400,
    description: 'Parámetros inválidos',
  })
  @ApiResponse({
    status: 403,
    description: 'Usuario no tiene configuración CTM',
  })
  async generateAlphanumericKey(
    @Body() generateKeyDto: GenerateKeyDto,
    @Request() req: any,
  ) {
    const userId = req.user.id;
    return this.keysService.generateAlphanumericKey(userId, generateKeyDto);
  }

  @Post('upload-to-ctm')
  @ApiOperation({ summary: 'Subir llave a CipherTrust Manager' })
  @ApiResponse({
    status: 201,
    description: 'Llave subida exitosamente a CTM',
  })
  @ApiResponse({
    status: 400,
    description: 'Parámetros inválidos',
  })
  @ApiResponse({
    status: 403,
    description: 'Usuario no tiene configuración CTM',
  })
  @ApiResponse({
    status: 409,
    description: 'La llave ya existe en CTM',
  })
  async uploadKeyToCtm(
    @Body() uploadKeyDto: UploadKeyDto,
    @Request() req: any,
  ) {
    const userId = req.user.id;
    return this.keysService.uploadKeyToCtm(userId, uploadKeyDto);
  }

  @Post('upload-batch-to-ctm')
  @ApiOperation({ summary: 'Subir múltiples llaves a CipherTrust Manager' })
  @ApiResponse({
    status: 201,
    description: 'Llaves subidas exitosamente a CTM',
  })
  @ApiResponse({
    status: 400,
    description: 'Parámetros inválidos',
  })
  @ApiResponse({
    status: 403,
    description: 'Usuario no tiene configuración CTM',
  })
  async uploadBatchKeysToCtm(
    @Body() uploadBatchKeysDto: UploadBatchKeysDto,
    @Request() req: any,
  ) {
    const userId = req.user.id;
    return this.keysService.uploadBatchKeysToCtm(userId, uploadBatchKeysDto);
  }

  @Get('ctm/:keyName/exists')
  @ApiOperation({ summary: 'Verificar si una llave existe en CTM' })
  @ApiParam({
    name: 'keyName',
    description: 'Nombre de la llave a verificar',
    example: 'my-encryption-key-001',
  })
  @ApiResponse({
    status: 200,
    description: 'Estado de existencia de la llave verificado',
  })
  @ApiResponse({
    status: 403,
    description: 'Usuario no tiene configuración CTM',
  })
  async checkKeyExists(
    @Param('keyName') keyName: string,
    @Request() req: any,
  ) {
    const userId = req.user.id;
    return this.keysService.checkKeyExists(userId, keyName);
  }

  @Get('ctm/auth/token')
  @ApiOperation({ summary: 'Obtener token de autenticación CTM' })
  @ApiResponse({
    status: 200,
    description: 'Token CTM obtenido exitosamente',
  })
  @ApiResponse({
    status: 403,
    description: 'Usuario no tiene configuración CTM',
  })
  async getCtmToken(@Request() req: any) {
    const userId = req.user.id;
    return this.keysService.getCtmToken(userId);
  }

  @Get()
  @ApiOperation({ summary: 'Obtener llaves del usuario' })
  @ApiResponse({
    status: 200,
    description: 'Lista de llaves del usuario',
    type: KeyListResponseDto,
  })
  @ApiResponse({
    status: 403,
    description: 'Usuario no tiene configuración CTM',
  })
  async getUserKeys(
    @Query() getKeysDto: GetKeysDto,
    @Request() req: any,
  ): Promise<KeyListResponseDto> {
    const userId = req.user.id;
    return this.keysService.getUserKeys(userId, getKeysDto);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Obtener una llave específica por ID' })
  @ApiParam({
    name: 'id',
    description: 'ID de la llave',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  @ApiResponse({
    status: 200,
    description: 'Llave encontrada',
    type: KeyResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Llave no encontrada',
  })
  async getUserKey(
    @Param('id') keyId: string,
    @Request() req: any,
  ): Promise<KeyResponseDto> {
    const userId = req.user.id;
    return this.keysService.getUserKey(userId, keyId);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Eliminar una llave por ID' })
  @ApiParam({
    name: 'id',
    description: 'ID de la llave',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  @ApiResponse({
    status: 200,
    description: 'Llave eliminada exitosamente',
  })
  @ApiResponse({
    status: 404,
    description: 'Llave no encontrada',
  })
  async deleteUserKey(
    @Param('id') keyId: string,
    @Request() req: any,
  ): Promise<{ message: string }> {
    const userId = req.user.id;
    await this.keysService.deleteUserKey(userId, keyId);
    return { message: 'Llave eliminada exitosamente' };
  }
}
