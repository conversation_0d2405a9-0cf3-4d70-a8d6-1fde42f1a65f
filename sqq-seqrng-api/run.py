"""
SeQRNG-CTM REST API Application Entry Point
"""

import os
from app import create_app
from app.config.environment import Environment

# Create Flask application
app = create_app()

if __name__ == '__main__':
    print("🚀 Starting SeQRNG-CTM REST API...")
    print(f"📊 API Version: {Environment.API_VERSION}")
    print(f"🌐 Host: {Environment.FLASK_HOST}")
    print(f"🔌 Port: {Environment.FLASK_PORT}")
    print(f"🐛 Debug: {Environment.FLASK_DEBUG}")
    print("📖 Documentation available at: /docs")
    print("🔍 Health check available at: /api/v1/health")
    
    app.run(
        host=Environment.FLASK_HOST,
        port=Environment.FLASK_PORT,
        debug=Environment.FLASK_DEBUG
    )
