"""
SeQRNG controller for key generation endpoints
"""

from flask import Blueprint, request, current_app
from app.utils.response_helpers import create_success_response, create_error_response
from app.utils.validators import validate_num_bytes, validate_packages
from app.services.seqrng_service import SeQRNGService
from app.services.ctm_service import CTMService

seqrng_bp = Blueprint('seqrng', __name__)


def get_seqrng_service():
    """Get SeQRNG service instance"""
    seqrng_config = current_app.config.get('SEQRNG_CONFIG')
    if not seqrng_config:
        raise Exception("SeQRNG configuration not loaded")
    return SeQRNGService(seqrng_config)


@seqrng_bp.route('/api/v1/keys/generate/bytes', methods=['POST'])
def generate_random_bytes():
    """Generate random bytes from SeQRNG"""
    try:
        # Get request parameters
        data = request.get_json() or {}
        num_bytes = data.get('num_bytes', 32)
        packages = data.get('packages', 1)

        # Validate parameters
        num_bytes_error = validate_num_bytes(num_bytes)
        if num_bytes_error:
            return create_error_response(num_bytes_error, 400, "validation_error")

        packages_error = validate_packages(packages)
        if packages_error:
            return create_error_response(packages_error, 400, "validation_error")

        # Generate random bytes
        seqrng_service = get_seqrng_service()
        result = seqrng_service.generate_random_bytes(num_bytes, packages)

        # Remove raw bytes from response (keep only base64)
        response_data = {
            "random_bytes_base64": result["random_bytes_base64"],
            "num_bytes": result["num_bytes"],
            "packages": result["packages"],
            "entropy_report": result["entropy_report"]
        }

        return create_success_response(response_data, "Random bytes generated successfully")

    except Exception as e:
        return create_error_response(f"Failed to generate random bytes: {str(e)}", 500, "generation_error")


@seqrng_bp.route('/api/v1/keys/generate/hex', methods=['POST'])
def generate_hex_key():
    """Generate random hexadecimal key from SeQRNG"""
    try:
        # Get request parameters
        data = request.get_json() or {}
        num_bytes = data.get('num_bytes', 32)

        # Validate parameters
        num_bytes_error = validate_num_bytes(num_bytes)
        if num_bytes_error:
            return create_error_response(num_bytes_error, 400, "validation_error")

        # Generate hex key
        seqrng_service = get_seqrng_service()
        result = seqrng_service.generate_hex_key(num_bytes)

        return create_success_response(result, "Hexadecimal key generated successfully")

    except Exception as e:
        return create_error_response(f"Failed to generate hex key: {str(e)}", 500, "generation_error")


@seqrng_bp.route('/api/v1/keys/generate/alphanumeric', methods=['POST'])
def generate_alphanumeric_key():
    """Generate random alphanumeric key from SeQRNG"""
    try:
        # Get request parameters
        data = request.get_json() or {}
        num_bytes = data.get('num_bytes', 32)

        # Validate parameters
        num_bytes_error = validate_num_bytes(num_bytes)
        if num_bytes_error:
            return create_error_response(num_bytes_error, 400, "validation_error")

        # Generate alphanumeric key
        seqrng_service = get_seqrng_service()
        result = seqrng_service.generate_alphanumeric_key(num_bytes)

        return create_success_response(result, "Alphanumeric key generated successfully")

    except Exception as e:
        return create_error_response(f"Failed to generate alphanumeric key: {str(e)}", 500, "generation_error")


# Dynamic CTM Configuration Endpoints

@seqrng_bp.route('/api/v1/keys/generate/bytes/dynamic', methods=['POST'])
def generate_random_bytes_dynamic():
    """Generate random bytes from SeQRNG with dynamic CTM configuration"""
    try:
        # Get request parameters
        data = request.get_json() or {}
        num_bytes = data.get('num_bytes', 32)
        packages = data.get('packages', 1)
        ctm_config = data.get('ctm_config')

        # Validate parameters
        num_bytes_error = validate_num_bytes(num_bytes)
        if num_bytes_error:
            return create_error_response(num_bytes_error, 400, "validation_error")

        packages_error = validate_packages(packages)
        if packages_error:
            return create_error_response(packages_error, 400, "validation_error")

        if not ctm_config:
            return create_error_response("CTM configuration is required for dynamic endpoints", 400, "validation_error")

        # Validate CTM configuration
        required_ctm_fields = ['ip_address', 'username', 'password', 'domain']
        for field in required_ctm_fields:
            if not ctm_config.get(field):
                return create_error_response(f"CTM configuration missing required field: {field}", 400, "validation_error")

        # Generate random bytes
        seqrng_service = get_seqrng_service()
        result = seqrng_service.generate_random_bytes(num_bytes, packages)

        # Remove raw bytes from response (keep only base64)
        response_data = {
            "random_bytes_base64": result["random_bytes_base64"],
            "num_bytes": result["num_bytes"],
            "packages": result["packages"],
            "entropy_report": result["entropy_report"]
        }

        return create_success_response(response_data, "Random bytes generated successfully")

    except Exception as e:
        return create_error_response(f"Failed to generate random bytes: {str(e)}", 500, "generation_error")


@seqrng_bp.route('/api/v1/keys/generate/hex/dynamic', methods=['POST'])
def generate_hex_key_dynamic():
    """Generate random hexadecimal key from SeQRNG with dynamic CTM configuration"""
    try:
        # Get request parameters
        data = request.get_json() or {}
        num_bytes = data.get('num_bytes', 32)
        ctm_config = data.get('ctm_config')

        # Validate parameters
        num_bytes_error = validate_num_bytes(num_bytes)
        if num_bytes_error:
            return create_error_response(num_bytes_error, 400, "validation_error")

        if not ctm_config:
            return create_error_response("CTM configuration is required for dynamic endpoints", 400, "validation_error")

        # Validate CTM configuration
        required_ctm_fields = ['ip_address', 'username', 'password', 'domain']
        for field in required_ctm_fields:
            if not ctm_config.get(field):
                return create_error_response(f"CTM configuration missing required field: {field}", 400, "validation_error")

        # Generate hex key
        seqrng_service = get_seqrng_service()
        result = seqrng_service.generate_hex_key(num_bytes)

        return create_success_response(result, "Hexadecimal key generated successfully")

    except Exception as e:
        return create_error_response(f"Failed to generate hex key: {str(e)}", 500, "generation_error")


@seqrng_bp.route('/api/v1/keys/generate/alphanumeric/dynamic', methods=['POST'])
def generate_alphanumeric_key_dynamic():
    """Generate random alphanumeric key from SeQRNG with dynamic CTM configuration"""
    try:
        # Get request parameters
        data = request.get_json() or {}
        num_bytes = data.get('num_bytes', 32)
        ctm_config = data.get('ctm_config')

        # Validate parameters
        num_bytes_error = validate_num_bytes(num_bytes)
        if num_bytes_error:
            return create_error_response(num_bytes_error, 400, "validation_error")

        if not ctm_config:
            return create_error_response("CTM configuration is required for dynamic endpoints", 400, "validation_error")

        # Validate CTM configuration
        required_ctm_fields = ['ip_address', 'username', 'password', 'domain']
        for field in required_ctm_fields:
            if not ctm_config.get(field):
                return create_error_response(f"CTM configuration missing required field: {field}", 400, "validation_error")

        # Generate alphanumeric key
        seqrng_service = get_seqrng_service()
        result = seqrng_service.generate_alphanumeric_key(num_bytes)

        return create_success_response(result, "Alphanumeric key generated successfully")

    except Exception as e:
        return create_error_response(f"Failed to generate alphanumeric key: {str(e)}", 500, "generation_error")
