import { IsString, IsOptional, <PERSON>Int, <PERSON>, Max, IsBoolean, IsEnum } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export enum KeyAlgorithm {
  AES = 'AES',
  RSA = 'RSA',
  HMAC = 'HMAC',
}

export class UploadKeyDto {
  @ApiProperty({
    example: 'my-encryption-key-001',
    description: 'Nombre de la llave en CTM',
  })
  @IsString()
  key_name: string;

  @ApiProperty({
    example: KeyAlgorithm.AES,
    description: 'Algoritmo de la llave',
    enum: KeyAlgorithm,
    required: false,
  })
  @IsOptional()
  @IsEnum(KeyAlgorithm)
  algorithm?: KeyAlgorithm = KeyAlgorithm.AES;

  @ApiProperty({
    example: 32,
    description: 'Número de bytes para la llave',
    minimum: 1,
    maximum: 1024,
    required: false,
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(1024)
  num_bytes?: number = 32;

  @ApiProperty({
    example: 'api_user',
    description: 'Propietario de la llave en CTM',
    required: false,
  })
  @IsOptional()
  @IsString()
  owner?: string = 'api_user';

  @ApiProperty({
    example: false,
    description: 'Si la llave es exportable',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  exportable?: boolean = false;

  @ApiProperty({
    example: 'base64-encoded-key-material',
    description: 'Material de llave en base64 (opcional, se genera si no se proporciona)',
    required: false,
  })
  @IsOptional()
  @IsString()
  key_material_base64?: string;
}
