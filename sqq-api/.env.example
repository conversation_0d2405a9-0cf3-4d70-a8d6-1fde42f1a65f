# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=password
DB_DATABASE=sqq_api

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=1h
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-this-in-production
JWT_REFRESH_EXPIRES_IN=7d

# Application Configuration
PORT=3000
NODE_ENV=development

# Security
BCRYPT_ROUNDS=12

# SeQRNG API Configuration
SEQRNG_API_URL=http://localhost:5000

# Default Admin User (created automatically if no users exist)
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=Admin123!
