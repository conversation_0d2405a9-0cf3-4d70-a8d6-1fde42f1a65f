import { IsOptional, IsEnum, IsInt, Min, Max, IsString, IsBoolean } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { KeyType, KeyAlgorithm, KeyStatus } from '../entities/key.entity';

export class GetKeysDto {
  @ApiProperty({
    example: 1,
    description: 'Número de página',
    minimum: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiProperty({
    example: 10,
    description: 'Número de elementos por página',
    minimum: 1,
    maximum: 100,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 10;

  @ApiProperty({
    example: KeyType.HEX_KEY,
    description: 'Filtrar por tipo de llave',
    enum: KeyType,
    required: false,
  })
  @IsOptional()
  @IsEnum(KeyType)
  type?: KeyType;

  @ApiProperty({
    example: KeyAlgorithm.AES,
    description: 'Filtrar por algoritmo',
    enum: KeyAlgorithm,
    required: false,
  })
  @IsOptional()
  @IsEnum(KeyAlgorithm)
  algorithm?: KeyAlgorithm;

  @ApiProperty({
    example: KeyStatus.UPLOADED_TO_CTM,
    description: 'Filtrar por estado',
    enum: KeyStatus,
    required: false,
  })
  @IsOptional()
  @IsEnum(KeyStatus)
  status?: KeyStatus;

  @ApiProperty({
    example: true,
    description: 'Filtrar por llaves subidas a CTM',
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsBoolean()
  uploadedToCtm?: boolean;

  @ApiProperty({
    example: 'my-key',
    description: 'Buscar por nombre de llave',
    required: false,
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiProperty({
    example: 'createdAt',
    description: 'Campo para ordenar',
    enum: ['createdAt', 'updatedAt', 'name', 'type', 'status'],
    required: false,
  })
  @IsOptional()
  @IsString()
  sortBy?: string = 'createdAt';

  @ApiProperty({
    example: 'DESC',
    description: 'Dirección del ordenamiento',
    enum: ['ASC', 'DESC'],
    required: false,
  })
  @IsOptional()
  @IsString()
  sortOrder?: 'ASC' | 'DESC' = 'DESC';
}
