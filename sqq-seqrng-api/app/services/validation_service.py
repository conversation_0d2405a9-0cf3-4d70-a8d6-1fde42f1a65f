"""
Validation service for business logic validation
"""

from typing import List
from app.utils.validators import validate_algorithm_key_length


class ValidationService:
    """Service class for validation operations"""
    
    @staticmethod
    def validate_key_upload_request(key_name: str, algorithm: str, num_bytes: int, 
                                  owner: str, exportable: bool) -> List[str]:
        """
        Validate key upload request parameters
        
        Args:
            key_name: Key name
            algorithm: Algorithm name
            num_bytes: Number of bytes
            owner: Key owner
            exportable: Whether key is exportable
            
        Returns:
            List of validation error messages (empty if valid)
        """
        errors = []
        
        # Validate key name
        from app.utils.validators import validate_key_name
        key_name_error = validate_key_name(key_name)
        if key_name_error:
            errors.append(key_name_error)
        
        # Validate algorithm
        if not algorithm or not isinstance(algorithm, str):
            errors.append("Algorithm must be a non-empty string")
        
        # Validate num_bytes
        from app.utils.validators import validate_num_bytes
        num_bytes_error = validate_num_bytes(num_bytes)
        if num_bytes_error:
            errors.append(num_bytes_error)
        
        # Validate algorithm and key length combination
        if algorithm and isinstance(num_bytes, int):
            if not validate_algorithm_key_length(algorithm, num_bytes):
                errors.append(f"Invalid key length {num_bytes} for algorithm {algorithm}")
        
        # Validate owner
        if not owner or not isinstance(owner, str):
            errors.append("Owner must be a non-empty string")
        
        # Validate exportable
        if not isinstance(exportable, bool):
            errors.append("Exportable must be a boolean value")
        
        return errors
    
    @staticmethod
    def validate_batch_upload_request(key_base_name: str, algorithm: str, num_bytes: int,
                                    key_count: int, owner: str, exportable: bool) -> List[str]:
        """
        Validate batch key upload request parameters
        
        Args:
            key_base_name: Base key name
            algorithm: Algorithm name
            num_bytes: Number of bytes
            key_count: Number of keys
            owner: Key owner
            exportable: Whether keys are exportable
            
        Returns:
            List of validation error messages (empty if valid)
        """
        errors = []
        
        # Validate base key name
        from app.utils.validators import validate_key_name
        key_name_error = validate_key_name(key_base_name)
        if key_name_error:
            errors.append(f"Base key name: {key_name_error}")
        
        # Validate key count
        from app.utils.validators import validate_key_count
        key_count_error = validate_key_count(key_count)
        if key_count_error:
            errors.append(key_count_error)
        
        # Use single key validation for other parameters
        single_key_errors = ValidationService.validate_key_upload_request(
            key_base_name, algorithm, num_bytes, owner, exportable
        )
        
        # Filter out key name error since we already validated base name
        filtered_errors = [error for error in single_key_errors 
                          if not error.startswith("Key name")]
        
        errors.extend(filtered_errors)
        
        return errors
