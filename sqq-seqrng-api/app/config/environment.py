"""
Environment configuration constants
"""

import os


class Environment:
    """Environment configuration constants"""
    
    # Flask configuration
    FLASK_DEBUG = os.environ.get('FLASK_DEBUG', 'False').lower() == 'true'
    FLASK_PORT = int(os.environ.get('PORT', 5000))
    FLASK_HOST = os.environ.get('FLASK_HOST', '0.0.0.0')
    
    # API configuration
    API_VERSION = "1.0.0"
    API_TITLE = "SeQRNG-CTM REST API"
    API_DESCRIPTION = "REST API for integrating SeQRNG quantum random number generator with Thales CipherTrust Manager"
    
    # Validation limits
    MAX_BYTES = 1024
    MIN_BYTES = 1
    MAX_PACKAGES = 100
    MIN_PACKAGES = 1
    MAX_KEY_COUNT = 100
    MIN_KEY_COUNT = 1
    MAX_KEY_NAME_LENGTH = 100
