import { ApiProperty } from '@nestjs/swagger';
import { KeyType, KeyAlgorithm, KeyStatus } from '../entities/key.entity';

export class KeyResponseDto {
  @ApiProperty({
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
    description: 'ID único de la llave',
  })
  id: string;

  @ApiProperty({
    example: 'my-encryption-key-001',
    description: 'Nombre de la llave',
  })
  name: string;

  @ApiProperty({
    example: KeyType.HEX_KEY,
    description: 'Tipo de llave generada',
    enum: KeyType,
  })
  type: KeyType;

  @ApiProperty({
    example: KeyAlgorithm.AES,
    description: 'Algoritmo de la llave',
    enum: KeyAlgorithm,
    nullable: true,
  })
  algorithm: KeyAlgorithm | null;

  @ApiProperty({
    example: 32,
    description: 'Número de bytes de la llave',
  })
  numBytes: number;

  @ApiProperty({
    example: KeyStatus.UPLOADED_TO_CTM,
    description: 'Estado actual de la llave',
    enum: KeyStatus,
  })
  status: KeyStatus;

  @ApiProperty({
    example: 'api_user',
    description: 'Propietario de la llave en CTM',
    nullable: true,
  })
  owner: string | null;

  @ApiProperty({
    example: false,
    description: 'Si la llave es exportable',
  })
  exportable: boolean;

  @ApiProperty({
    example: true,
    description: 'Si la llave fue subida a CTM',
  })
  uploadedToCtm: boolean;

  @ApiProperty({
    example: 'my-encryption-key-001',
    description: 'Nombre de la llave en CTM',
    nullable: true,
  })
  ctmKeyName: string | null;

  @ApiProperty({
    example: '{"entropy_status":"success","entropy_string":"0.871358","error_string":"0.99228"}',
    description: 'Reporte de entropía en formato JSON',
    nullable: true,
  })
  entropyReport: string | null;

  @ApiProperty({
    example: null,
    description: 'Mensaje de error si la operación falló',
    nullable: true,
  })
  errorMessage: string | null;

  @ApiProperty({
    example: '2025-07-09T21:30:00.000Z',
    description: 'Fecha de creación',
  })
  createdAt: Date;

  @ApiProperty({
    example: '2025-07-09T21:30:00.000Z',
    description: 'Fecha de última actualización',
  })
  updatedAt: Date;

  @ApiProperty({
    example: true,
    description: 'Si la operación fue exitosa',
  })
  isSuccessful: boolean;

  @ApiProperty({
    example: 'my-encryption-key-001',
    description: 'Nombre para mostrar (CTM o nombre original)',
  })
  displayName: string;
}

export class KeyListResponseDto {
  @ApiProperty({
    type: [KeyResponseDto],
    description: 'Lista de llaves del usuario',
  })
  keys: KeyResponseDto[];

  @ApiProperty({
    example: 25,
    description: 'Total de llaves',
  })
  total: number;

  @ApiProperty({
    example: 20,
    description: 'Llaves exitosas',
  })
  successful: number;

  @ApiProperty({
    example: 5,
    description: 'Llaves fallidas',
  })
  failed: number;

  @ApiProperty({
    example: 15,
    description: 'Llaves subidas a CTM',
  })
  uploadedToCtm: number;
}
