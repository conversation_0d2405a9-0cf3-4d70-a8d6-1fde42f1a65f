"""
SeQRNG-CTM REST API Application Factory
"""

from flask import Flask
from flask_cors import CORS
import os


def create_app(config_name=None):
    """
    Application factory pattern for creating Flask app instances
    
    Args:
        config_name: Configuration environment name
        
    Returns:
        Flask application instance
    """
    app = Flask(__name__)
    
    # Enable CORS for all routes
    CORS(app)
    
    # Configuration
    app.config['JSON_SORT_KEYS'] = False
    
    # Load configuration
    from app.config.settings import Config
    config = Config()
    
    # Validate and load configuration
    if not config.validate_config():
        raise Exception("Configuration validation failed. Please check your .env file or environment variables.")
    
    # Store configuration in app context
    app.config['SEQRNG_CONFIG'] = config.get_seqrng_config()
    app.config['CTM_CONFIG'] = config.get_ctm_config()
    
    # Register error handlers
    register_error_handlers(app)
    
    # Register blueprints
    register_blueprints(app)
    
    return app


def register_error_handlers(app):
    """Register global error handlers"""
    from app.utils.response_helpers import create_error_response
    
    @app.errorhandler(404)
    def not_found(error):
        return create_error_response("Endpoint not found", 404, "not_found")
    
    @app.errorhandler(405)
    def method_not_allowed(error):
        return create_error_response("Method not allowed", 405, "method_not_allowed")
    
    @app.errorhandler(500)
    def internal_error(error):
        return create_error_response("Internal server error", 500, "internal_error")


def register_blueprints(app):
    """Register application blueprints"""
    from app.controllers.health_controller import health_bp
    from app.controllers.seqrng_controller import seqrng_bp
    from app.controllers.ctm_controller import ctm_bp
    
    # Register blueprints
    app.register_blueprint(health_bp)
    app.register_blueprint(seqrng_bp)
    app.register_blueprint(ctm_bp)
